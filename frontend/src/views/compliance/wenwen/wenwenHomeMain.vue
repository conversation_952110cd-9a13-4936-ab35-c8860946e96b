<template>
  <div class="homeLeft">
    <div class="wenwen-left" :class="!showMenu && 'collapse'">
      <wenwen-left-tab ref="wenwenleftTab"
        @wenClickChart="wenClickChart" @sendChatContent="sendChatContent" @getSettingData="getSettingData">
      </wenwen-left-tab>
    </div>
    <div class="wenwen-right" :class="!showMenu && 'collapse'">
      <div class="wenwen-collapse" @click="showMenu = false" v-if="showMenu">
        <i class="iconfont ic-arrow-left"  style="color:  #CF1A1C"></i>
        <span>工具栏1</span>
      </div>
      <div class="wenwen-collapse" @click="showMenu = true" v-if="!showMenu">
        <i class="iconfont ic-arrow-right" style="color:  #CF1A1C"></i>
        <span>工具栏2</span>
      </div>
        <wenwen-home-index ref="wenwenhomeIndex" :settingParam="settingParam"  @getChatData="getChatData" @editChatSure="editChatSure" @getCreateChat="getCreateChat"  @getKeywordNum="getKeywordNum"></wenwen-home-index>
     </div>
  </div>
</template>
<script>
import WenwenHomeIndex from "@/views/compliance/wenwen/wenwenHomeIndex.vue";
import WenwenLeftTab from "@/views/compliance/wenwen/wenwenLeft-tab.vue";

export default {
  data () {
    return {
      historyParam: {
        currentChat: '', // 当前会话id
        chatList: [], // 聊天会话列表
        chatListNum: 0,
        belongsPlate: '',
        companyCode: '',
        userNamePinYin: '',
        keywordAll: 0,
        keywordNum: 0,
        keyword: '',
        plusStatus: ''
      },
      settingParam: {
        fact: '', // 是否根据事实回答选则
        continuousChat: '', // 是否连续会话（默认否；0 是 ；1 否）
        temperature: '',
        promptId: '',
        promptName: '',
        prompt: '',
        userNamePinYin: '',
        chatModel: '' // 聊天模式（1：日常问答，2：专业模式）
      },
      showMenu: false
    }
  },
  components: { WenwenLeftTab, WenwenHomeIndex },
  computed: {},
  methods: {
    wenClickChart (data) {
      this.historyParam = data;
      this.$refs.wenwenhomeIndex.currentChat = data.currentChat;
      this.$refs.wenwenhomeIndex.chatList = data.chatList;
      this.$refs.wenwenhomeIndex.chatListNum = data.chatListNum;
      if (data.setBelongsPlateFlag === '1') {
        this.$refs.wenwenhomeIndex.belongsPlate = data.belongsPlate;
      }
      this.$refs.wenwenhomeIndex.companyCode = data.companyCode;
      this.$refs.wenwenhomeIndex.userNamePinYin = data.userNamePinYin;
      this.$refs.wenwenhomeIndex.plusStatus = data.plusStatus;
      this.$refs.wenwenhomeIndex.keywordQuery = data.keyword; // 清除当前对话框标红关键词
    },
    getSettingData () {
      this.settingParam = this.$refs.wenwenleftTab.settingParam;
    },
    getCreateChat (chatName, name, type, index) {
      this.$refs.wenwenleftTab.getCreateChat(chatName, name, type, index);
    },
    sendChatContent (name) {
      this.$refs.wenwenhomeIndex.sendChatContent(name);
    },
    getKeywordNum (indexAll, indexNum) {
      this.historyParam.keywordAll = indexAll;
      this.historyParam.keywordNum = indexNum;
      this.$refs.wenwenleftTab.historyParam.keywordAll = indexAll;
      this.$refs.wenwenleftTab.historyParam.keywordNum = indexNum;
    },
    getChatData (val) {
      this.$refs.wenwenleftTab.getChatData(val);
    },
    editChatSure (name) {
      this.$refs.wenwenleftTab.editChatSure(name);
    }
  },
  mounted () {},
  beforeDestroy () {},
  created () {
  }
}
</script>

<style  scoped lang="scss">
.homeLeft{
    display:flex;
    height: calc(100% - 49px - 60px);
    width: 100%;
    background-color: #fff;
    background-image: url('../../../assets/images/wenwen/background.svg');
    background-size:cover;
    background-repeat: no-repeat;
    position:relative;
    background-position: center;
    .wenwen-left{
      width:300px;
      position:relative;
      background: rgba(252, 254, 255, 0.6);
      transition: all 0.3s;
      &.collapse{
        transform: translateX(-300px - 72px);
      }
    }
    .wenwen-right{
      flex:1;
      position:relative;
      display:flex;
      flex-direction: column;
      width:calc(100% - 72px);
      transition: all 0.3s;
      &.collapse{
        margin-left:-300px;
      }
      .wenwen-collapse{
        position:absolute;
        z-index: 999;
        background-image:url('../../../assets/images/wenwen/btn.png');
        top:40px;
        background-repeat: no-repeat;
        height:100px;
        width:26px;
        cursor: pointer;
        text-align: center;
        font-size: 14px;
        padding-top: 22px;
        line-height: 1.2;
        color: #CF1A1C;
        background-size: 100% 100%;
        .iconfont {
          font-size:14px;
        }
        &:hover {
          color: #CF1A1C;
          .iconfont {
            color: #CF1A1C;
          }
        }
      }
    }
    .wenwen-expand{
      position:absolute;
      top:40%;
      left:0;
      text-align: center;
      cursor: pointer;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      z-index:999;
      &:hover {
        .iconfont {
          color: #CF1A1C;
        }
      }
    }
}
</style>
