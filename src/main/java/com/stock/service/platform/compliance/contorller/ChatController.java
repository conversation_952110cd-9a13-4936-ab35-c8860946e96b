package com.stock.service.platform.compliance.contorller;

import com.stock.core.controller.BaseController;
import com.stock.core.dto.JsonResponse;
import com.stock.core.util.JsonUtil;
import com.stock.service.platform.common.entity.ChatRecord;
import com.stock.service.platform.compliance.dto.*;
import com.stock.service.platform.compliance.service.ChatService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/chat")
@RequiredArgsConstructor
public class ChatController extends BaseController {

    private final ChatService chatService;

    //   流式问答
    @PostMapping(value = "/message/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<Completions> streamMessage(@RequestBody QuestionDto questionDto, HttpServletRequest request) {
        return chatService.streamMessage(questionDto,request);
    }

    //   新建聊天窗口
    @PostMapping(value = "/createChat")
    public JsonResponse<ChatResponseDto> createChat(@RequestBody ChatRecord record) {
        JsonResponse<ChatResponseDto> response = new JsonResponse<>();
        response.setResult(chatService.createChat(record));
        return response;
    }

    //   获取模型列表
    @RequestMapping(value = "/getModelList")
    public JsonResponse<List<ModelDto>> getModelList() {
        JsonResponse<List<ModelDto>> response = new JsonResponse<>();
        response.setResult(chatService.getModelList(getUserInfo().getUserId()));
        return response;
    }

    //   获取标问
    @RequestMapping(value = "/getAiAssistantStandardIssues")
    public JsonResponse<List<String>> getAiAssistantStandardIssues() {
        JsonResponse<List<String>> response = new JsonResponse<>();
        response.setResult(chatService.getAiAssistantStandardIssues());
        return response;
    }

    //   获取公司代码或名称
    @RequestMapping("/checkQuestion")
    public JsonResponse<List<String>> checkQuestion(@RequestBody Map<String,String> question) {
        JsonResponse<List<String>> response = new JsonResponse<>();
        List<String> codeOrName = chatService.containsCompanyCodeOrName(question.get("question"));
        log.info("checkQuestion codeOrName {}", JsonUtil.toJson(codeOrName));
        response.setResult(codeOrName);
        return response;
    }

    //   获取历史会话列表
    @RequestMapping(value = "/getChatRecordList")
    public JsonResponse<ChatResponseDto> getChatRecordList() {
        JsonResponse<ChatResponseDto> response = new JsonResponse<>();
        response.setResult(chatService.getChatRecordList());
        return response;
    }

    //   单个对话
    @RequestMapping(value = "/getChatContent")
    public JsonResponse<ChatResponseDto> getChatContent(@RequestBody ChatDongFormDto chatDongFormDto) {
        JsonResponse<ChatResponseDto> response = new JsonResponse<>();
        response.setResult(chatService.getChatContent(chatDongFormDto));
        return response;
    }

    //   右侧资料
    @GetMapping(value = "/getMaterial")
    public JsonResponse<MaterialResponse> getMaterial(@RequestParam("contentId") String contentId) {
        JsonResponse<MaterialResponse> response = new JsonResponse<>();
        response.setResult(chatService.getMaterial(contentId));
        return response;
    }

    //   修改聊天窗口名称
    @RequestMapping(value = "/editChatName")
    public JsonResponse<Boolean> editChatName(@RequestBody ChatDongFormDto chatDongFormDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(chatService.editChatName(chatDongFormDto));
        response.setSuccess(true);
        return response;
    }

    //   删除会话
    @RequestMapping(value = "/delete")
    public JsonResponse<ChatResponseDto> delete(ChatDongFormDto chatDongFormDto) {
        JsonResponse<ChatResponseDto> response = new JsonResponse<>();
        response.setResult(chatService.delete(chatDongFormDto));
        response.setSuccess(true);
        return response;
    }

    //   赞踩接口
    @RequestMapping(value = "/getFeedbackType")
    public JsonResponse<String> getFeedbackType(@RequestBody Map<String, String> map) {
        JsonResponse<String> response = new JsonResponse<>();
        response.setResult(chatService.getFeedbackType(map));
        return response;
    }

    //   获取token
    @RequestMapping(value = "/getTenantInfo")
    public JsonResponse<Map<String, String>> getTenantInfo() {
        JsonResponse<Map<String, String>> response = new JsonResponse<>();
        Map<String, String> mapA = chatService.getTenantInfo();
        response.setResult(mapA);
        return response;
    }

    //   提交反馈
    @RequestMapping(value = "/submitFeedback")
    public JsonResponse<Boolean> submitFeedback(@RequestBody ChatFeedbackDto feedbackDto) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(chatService.submitFeedback(feedbackDto));
        return response;
    }

    //   删除反馈
    @RequestMapping(value = "/deleteFeedback")
    public JsonResponse<Boolean> deleteFeedback(@RequestParam String chatContentId) {
        JsonResponse<Boolean> response = new JsonResponse<>();
        response.setResult(chatService.deleteFeedback(chatContentId));
        return response;
    }
}
